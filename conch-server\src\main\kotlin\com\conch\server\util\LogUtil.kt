package com.conch.server.util

import org.slf4j.LoggerFactory
import java.io.File
import java.io.PrintStream
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * 日志输出工具类
 * 解决中文乱码问题
 */
object LogUtil {
    
    private val logger = LoggerFactory.getLogger(LogUtil::class.java)
    
    /**
     * 输出指令日志（使用英文避免编码问题）
     */
    fun logCommand(
        action: String,
        deviceModel: String,
        command: String,
        sessionId: String,
        timestamp: String
    ) {
        // 使用英文日志，避免编码问题
        logger.info("=".repeat(60))
        logger.info("[COMMAND RECEIVED] New user command")
        logger.info("Device Model: {}", deviceModel)
        logger.info("User Command: \"{}\"", command)
        logger.info("Session ID: {}", sessionId)
        logger.info("Received Time: {}", timestamp)
        logger.info("=".repeat(60))
    }
    
    /**
     * 输出脚本生成日志（使用英文避免编码问题）
     */
    fun logScriptGeneration(sessionId: String, scriptId: String, actionCount: Int) {
        logger.info("[SCRIPT GENERATION] Generated execution script for session {}", sessionId)
        logger.info("Script ID: {}", scriptId)
        logger.info("Action Count: {}", actionCount)
        logger.info("Generation Time: {}", java.time.LocalDateTime.now())
    }
    
    /**
     * 输出反馈日志（使用英文避免编码问题）
     */
    fun logFeedback(
        sessionId: String,
        scriptId: String,
        status: String,
        executionTime: Long,
        completedActions: Int,
        totalActions: Int
    ) {
        logger.info("[EXECUTION FEEDBACK] Received client execution feedback")
        logger.info("Session ID: {}", sessionId)
        logger.info("Script ID: {}", scriptId)
        logger.info("Execution Status: {}", status)
        logger.info("Execution Time: {}ms", executionTime)
        logger.info("Completed Actions: {}/{}", completedActions, totalActions)
        logger.info("-".repeat(40))
    }

    /**
     * 保存App日志到文件
     */
    fun saveAppLogToFile(
        logId: String,
        deviceModel: String,
        logContent: String,
        timestamp: LocalDateTime
    ) {
        try {
            // 创建server-log目录（如果不存在）
            val logDir = File("server-log")
            if (!logDir.exists()) {
                logDir.mkdirs()
            }

            // 生成日志文件名：app-log-设备型号-时间戳.txt
            val formatter = DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")
            val timeStr = timestamp.format(formatter)
            val sanitizedDeviceModel = deviceModel.replace("[^a-zA-Z0-9-_]".toRegex(), "_")
            val fileName = "app-log-${sanitizedDeviceModel}-${timeStr}.txt"

            val logFile = File(logDir, fileName)

            // 写入日志内容
            logFile.writeText(buildString {
                appendLine("=".repeat(80))
                appendLine("App日志文件")
                appendLine("=".repeat(80))
                appendLine("日志ID: $logId")
                appendLine("设备型号: $deviceModel")
                appendLine("接收时间: $timestamp")
                appendLine("日志长度: ${logContent.length} 字符")
                appendLine("=".repeat(80))
                appendLine()
                appendLine("日志内容:")
                appendLine("-".repeat(80))
                appendLine(logContent)
                appendLine("-".repeat(80))
                appendLine("日志结束")
            }, Charsets.UTF_8)

            logger.info("App日志已保存到文件: {}", logFile.absolutePath)

        } catch (e: Exception) {
            logger.error("保存App日志文件失败", e)
            throw e
        }
    }
}
