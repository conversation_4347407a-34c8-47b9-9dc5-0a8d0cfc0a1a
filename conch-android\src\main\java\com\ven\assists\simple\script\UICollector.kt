package com.ven.assists.simple.script

import android.graphics.Rect
import com.google.gson.annotations.SerializedName
import com.ven.assists.AssistsCore
import com.ven.assists.AssistsCore.getBoundsInScreen
import com.ven.assists.simple.common.LogWrapper
import com.blankj.utilcode.util.ScreenUtils

/**
 * UI信息收集器
 * 用于收集当前界面的UI状态信息
 */
object UICollector {

    /**
     * 收集当前UI信息
     */
    fun collectCurrentUI(): UIInfo {
        LogWrapper.logAppend("开始收集UI信息")
        
        val packageName = AssistsCore.getPackageName()
        val allNodes = AssistsCore.getAllNodes()

        val uiElements = allNodes.mapNotNull { node ->
            val text = node.text?.toString()
            val className = node.className?.toString()
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            if (!text.isNullOrEmpty() || className != null) {
                UIElement(
                    text = text,
                    className = className,
                    bounds = ElementBounds(
                        left = bounds.left,
                        top = bounds.top,
                        right = bounds.right,
                        bottom = bounds.bottom
                    ),
                    clickable = node.isClickable,
                    scrollable = node.isScrollable,
                    viewId = node.viewIdResourceName
                )
            } else null
        }

        val uiInfo = UIInfo(
            packageName = packageName,
            activityName = getCurrentActivityName(),
            uiElements = uiElements,
            screenSize = ScreenSize(
                width = ScreenUtils.getScreenWidth(),
                height = ScreenUtils.getScreenHeight()
            )
        )

        LogWrapper.logAppend("UI信息收集完成，元素数量: ${uiElements.size}")
        return uiInfo
    }

    /**
     * 获取当前Activity名称
     */
    private fun getCurrentActivityName(): String {
        // 通过反射或其他方式获取当前Activity名称
        // 这里暂时返回未知，后续可以完善
        return "unknown"
    }
}

/**
 * UI信息
 */
data class UIInfo(
    @SerializedName("packageName")
    val packageName: String,
    
    @SerializedName("activityName")
    val activityName: String,
    
    @SerializedName("uiElements")
    val uiElements: List<UIElement>,
    
    @SerializedName("screenSize")
    val screenSize: ScreenSize
)

/**
 * UI元素
 */
data class UIElement(
    @SerializedName("text")
    val text: String?,
    
    @SerializedName("className")
    val className: String?,
    
    @SerializedName("bounds")
    val bounds: ElementBounds,
    
    @SerializedName("clickable")
    val clickable: Boolean,
    
    @SerializedName("scrollable")
    val scrollable: Boolean,
    
    @SerializedName("viewId")
    val viewId: String?
)

/**
 * 元素边界
 */
data class ElementBounds(
    @SerializedName("left")
    val left: Int,
    
    @SerializedName("top")
    val top: Int,
    
    @SerializedName("right")
    val right: Int,
    
    @SerializedName("bottom")
    val bottom: Int
)

/**
 * 屏幕尺寸
 */
data class ScreenSize(
    @SerializedName("width")
    val width: Int,
    
    @SerializedName("height")
    val height: Int
)
