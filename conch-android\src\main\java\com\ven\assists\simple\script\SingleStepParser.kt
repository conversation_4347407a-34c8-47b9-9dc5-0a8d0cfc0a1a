package com.ven.assists.simple.script

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.ven.assists.simple.common.LogWrapper

/**
 * 单步骤解析器
 * 用于解析服务端发送的单步骤JSON元数据
 */
class SingleStepParser {

    /**
     * 单步骤元数据
     */
    data class SingleStepMetadata(
        @SerializedName("sessionId")
        val sessionId: String,
        
        @SerializedName("stepId")
        val stepId: String,
        
        @SerializedName("stepName")
        val stepName: String,
        
        @SerializedName("stepDescription")
        val stepDescription: String,
        
        @SerializedName("targetApp")
        val targetApp: AppInfo?,
        
        @SerializedName("action")
        val action: ActionMetadata,
        
        @SerializedName("successConditions")
        val successConditions: List<ConditionMetadata>,
        
        @SerializedName("retryConfig")
        val retryConfig: RetryConfig,
        
        @SerializedName("needsFeedback")
        val needsFeedback: Boolean,
        
        @SerializedName("expectedNextAction")
        val expectedNextAction: String?
    )

    /**
     * 应用信息
     */
    data class AppInfo(
        @SerializedName("packageName")
        val packageName: String,
        
        @SerializedName("activityName")
        val activityName: String
    )

    /**
     * 动作元数据
     */
    data class ActionMetadata(
        @SerializedName("type")
        val type: String,
        
        @SerializedName("params")
        val params: Map<String, Any>,
        
        @SerializedName("timeout")
        val timeout: Long
    )

    /**
     * 条件元数据
     */
    data class ConditionMetadata(
        @SerializedName("type")
        val type: String,
        
        @SerializedName("params")
        val params: Map<String, Any>
    )

    /**
     * 重试配置
     */
    data class RetryConfig(
        @SerializedName("maxRetries")
        val maxRetries: Int,
        
        @SerializedName("retryDelay")
        val retryDelay: Long
    )

    /**
     * 从JSON字符串解析单步骤元数据
     */
    fun parseFromJson(jsonString: String): SingleStepMetadata? {
        return try {
            LogWrapper.logAppend("开始解析单步骤元数据")
            val metadata = Gson().fromJson(jsonString, SingleStepMetadata::class.java)
            LogWrapper.logAppend("解析成功: ${metadata.stepName}")
            metadata
        } catch (e: Exception) {
            LogWrapper.logAppend("解析单步骤元数据失败: ${e.message}")
            null
        }
    }
}
