package com.ven.assists.simple

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.KeyEvent
import android.view.View
import android.view.accessibility.AccessibilityEvent
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NotificationManagerCompat
import androidx.core.view.isVisible
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.PermissionUtils.SimpleCallback
import com.lxj.xpopup.XPopup
import com.ven.assists.AssistsCore
import com.ven.assists.AssistsCore.logNode
import com.ven.assists.service.AssistsService
import com.ven.assists.service.AssistsServiceListener
import com.ven.assists.simple.databinding.ActivityMainBinding
import com.ven.assists.simple.overlays.OverlayAdvanced
import com.ven.assists.simple.overlays.OverlayBasic
import com.ven.assists.simple.overlays.OverlayPro
import com.ven.assists.simple.overlays.OverlayWeb
import com.ven.assists.utils.CoroutineWrapper
import kotlinx.coroutines.delay
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import com.ven.assists.simple.network.HealthCheckResponseDto
import com.ven.assists.simple.common.LogWrapper
import com.ven.assists.simple.script.StepSessionManager

class MainActivity : AppCompatActivity(), AssistsServiceListener {
    private var isActivityResumed = false
    private val mainScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val handler = Handler(Looper.getMainLooper())
    private var connectionCheckRunnable: Runnable? = null

    // 分步会话管理器
    private val stepSessionManager = StepSessionManager()
    val viewBind: ActivityMainBinding by lazy {
        ActivityMainBinding.inflate(layoutInflater).apply {
            btnEnable.setOnClickListener {
                AssistsCore.openAccessibilitySetting()
                startActivity(Intent(this@MainActivity, SettingGuideActivity::class.java))
            }
            btnBasic.setOnClickListener {
                OverlayBasic.onClose = {
                    OverlayBasic.hide()
                }
                if (OverlayBasic.showed) {
                    OverlayBasic.hide()
                } else {
                    OverlayBasic.show()
                }
            }
            btnSendLogs.setOnClickListener {
                sendCurrentLogs()
            }
            btnRequestOpenWechat.setOnClickListener {
                requestOpenWechat()
            }
            btnPro.setOnClickListener {
                OverlayPro.onClose = {
                    OverlayPro.hide()
                }
                if (OverlayPro.showed) {
                    OverlayPro.hide()
                } else {
                    OverlayPro.show()
                }
            }
            btnAdvanced.setOnClickListener {
                OverlayAdvanced.onClose = {
                    OverlayAdvanced.hide()
                }
                if (OverlayAdvanced.showed) {
                    OverlayAdvanced.hide()
                } else {
                    OverlayAdvanced.show()
                }
            }
            btnWeb.setOnClickListener {
                OverlayWeb.onClose = {
                    OverlayWeb.hide()
                }
                if (OverlayWeb.showed) {
                    OverlayWeb.hide()
                } else {

                    OverlayWeb.show()
                }
            }
            btnNetworkTest.setOnClickListener {
                startActivity(Intent(this@MainActivity, NetworkTestActivity::class.java))
            }
            btnSendCommand.setOnClickListener {
                sendCustomCommand()
            }
        }
    }

    private var disableNotificationView: View? = null

    private lateinit var drawingView: MultiTouchDrawingView

    override fun onResume() {
        super.onResume()
        isActivityResumed = true
        checkServiceEnable()
    }

    override fun onPause() {
        super.onPause()
        isActivityResumed = false
    }

    private fun checkServiceEnable() {
        if (!isActivityResumed) return
        if (AssistsCore.isAccessibilityServiceEnabled()) {
            viewBind.btnEnable.isVisible = false
            viewBind.llOption.isVisible = true
        } else {
            viewBind.btnEnable.isVisible = true
            viewBind.llOption.isVisible = false
        }
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent) {
        super.onAccessibilityEvent(event)
//        if (event.eventType == AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED) {
//
//        }
    }

    override fun onServiceConnected(service: AssistsService) {
//        onBackApp()
        checkServiceEnable()
        AssistsCore.getAllNodes().forEach { it.logNode() }
        if (AssistsCore.getPackageName() != AppUtils.getAppPackageName()) {
            CoroutineWrapper.launch { AssistsCore.launchApp(AppUtils.getAppPackageName()) }
        }
    }

    private fun onBackApp() {
        CoroutineWrapper.launch {
            while (AssistsCore.getPackageName() != packageName) {
                AssistsCore.back()
                delay(500)
            }
        }
    }

    override fun onUnbind() {
        checkServiceEnable()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(viewBind.root)
        AssistsService.listeners.add(this)

        // 注册返回按钮回调
        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        // 初始化连接状态检查
        startConnectionStatusCheck()

        checkPermission()
    }

    private fun checkPermission() {
        val areNotificationsEnabled = NotificationManagerCompat.from(this).areNotificationsEnabled()
        if (!areNotificationsEnabled) {
            // 通知权限未开启，提示用户去设置
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                PermissionUtils.permission(Manifest.permission.POST_NOTIFICATIONS).callback(object : SimpleCallback {
                    override fun onGranted() {
                    }

                    override fun onDenied() {
                        showNotificationPermissionOpenDialog()
                    }
                }).request()
            } else {
                showNotificationPermissionOpenDialog()
            }
        }
    }

    private fun showNotificationPermissionOpenDialog() {
        XPopup.Builder(this).asConfirm("提示", "未开启通知权限，开启通知权限以获得完整测试相关通知提示") {
            val intent = Intent()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0及以上版本，跳转到应用的通知设置页面
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
            } else {
                // Android 8.0以下版本，跳转到应用详情页面
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.setData(Uri.parse("package:" + getPackageName()))
            }
            startActivity(intent)
        }.show()
    }

    /**
     * 发送自定义指令
     */
    private fun sendCustomCommand() {
        val commandText = viewBind.etCommandInput.text.toString().trim()
        if (commandText.isEmpty()) {
            viewBind.etCommandInput.error = "请输入指令内容"
            return
        }

        mainScope.launch {
            try {
                val result = App.networkManager.sendCommand("CUSTOM_COMMAND", commandText)
                result.onSuccess { response: com.ven.assists.simple.network.CommandResponseDto ->
                    // 清空输入框
                    viewBind.etCommandInput.setText("")
                    // 可以显示成功提示
                    Log.d("MainActivity", "自定义指令发送成功: ${response.message}")
                }.onFailure { error: Throwable ->
                    Log.e("MainActivity", "自定义指令发送失败", error)
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "发送自定义指令异常", e)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AssistsService.listeners.remove(this)
        connectionCheckRunnable?.let { handler.removeCallbacks(it) }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            moveTaskToBack(true)
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private val onBackPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            moveTaskToBack(true)
        }
    }

    /**
     * 开始连接状态检查
     */
    private fun startConnectionStatusCheck() {
        connectionCheckRunnable = object : Runnable {
            override fun run() {
                checkConnectionStatus()
                handler.postDelayed(this, 6000) // 每6秒检查一次
            }
        }
        handler.post(connectionCheckRunnable!!)
    }

    /**
     * 检查连接状态并更新UI
     */
    private fun checkConnectionStatus() {
        mainScope.launch {
            try {
                val result = App.networkManager.getServerStatus()
                result.onSuccess { status: HealthCheckResponseDto ->
                    updateConnectionUI(true, "服务端连接状态: 已连接 (${status.server.name})")
                }.onFailure { _: Throwable ->
                    updateConnectionUI(false, "服务端连接状态: 连接失败")
                }
            } catch (e: Exception) {
                updateConnectionUI(false, "服务端连接状态: 未连接")
            }
        }
    }

    /**
     * 更新连接状态UI
     */
    private fun updateConnectionUI(isConnected: Boolean, statusText: String) {
        viewBind.tvConnectionStatus.text = statusText

        // 创建圆形背景并设置颜色
        val drawable = resources.getDrawable(R.drawable.connection_indicator_bg, null).mutate()
        drawable.setTint(if (isConnected) Color.GREEN else Color.RED)
        viewBind.connectionIndicator.background = drawable
    }

    /**
     * 发送当前日志到服务端
     */
    private fun sendCurrentLogs() {
        mainScope.launch {
            try {
                // 获取当前日志内容
                val logContent = LogWrapper.logCache.toString()

                if (logContent.isEmpty()) {
                    Toast.makeText(this@MainActivity, "当前没有日志内容", Toast.LENGTH_SHORT).show()
                    return@launch
                }

                // 显示发送中状态
                viewBind.btnSendLogs.isEnabled = false
                viewBind.btnSendLogs.text = "发送中..."

                // 发送日志到服务端
                val result = App.networkManager.uploadLogs(logContent)

                result.onSuccess { response ->
                    Toast.makeText(this@MainActivity, "日志发送成功: ${response.message}", Toast.LENGTH_SHORT).show()
                    LogWrapper.logAppend("日志已发送到服务端")
                }.onFailure { error ->
                    Toast.makeText(this@MainActivity, "日志发送失败: ${error.message}", Toast.LENGTH_LONG).show()
                    LogWrapper.logAppend("日志发送失败: ${error.message}")
                }

            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "发送日志时出错: ${e.message}", Toast.LENGTH_LONG).show()
                LogWrapper.logAppend("发送日志异常: ${e.message}")
            } finally {
                // 恢复按钮状态
                viewBind.btnSendLogs.isEnabled = true
                viewBind.btnSendLogs.text = "发送当前日志"
            }
        }
    }

    /**
     * 请求打开微信
     */
    private fun requestOpenWechat() {
        mainScope.launch {
            try {
                // 显示请求中状态
                viewBind.btnRequestOpenWechat.isEnabled = false
                viewBind.btnRequestOpenWechat.text = "请求中..."

                LogWrapper.logAppend("发送请求：打开微信")

                // 发送请求到服务端
                val result = App.networkManager.requestOpenWechat()

                result.onSuccess { response ->
                    Toast.makeText(this@MainActivity, "请求成功: ${response.message}", Toast.LENGTH_SHORT).show()
                    LogWrapper.logAppend("服务端响应: ${response.message}")

                    // 如果服务端返回了脚本数据，执行动态脚本
                    response.scriptData?.let { scriptData ->
                        LogWrapper.logAppend("收到服务端脚本数据:")
                        LogWrapper.logAppend(scriptData)

                        // 执行动态脚本
                        LogWrapper.logAppend("=== 开始执行动态脚本 ===")
                        stepSessionManager.executeStep(scriptData)
                    }

                }.onFailure { error ->
                    Toast.makeText(this@MainActivity, "请求失败: ${error.message}", Toast.LENGTH_LONG).show()
                    LogWrapper.logAppend("请求打开微信失败: ${error.message}")
                }

            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "请求时出错: ${e.message}", Toast.LENGTH_LONG).show()
                LogWrapper.logAppend("请求打开微信异常: ${e.message}")
            } finally {
                // 恢复按钮状态
                viewBind.btnRequestOpenWechat.isEnabled = true
                viewBind.btnRequestOpenWechat.text = "请求打开微信"
            }
        }
    }
}
