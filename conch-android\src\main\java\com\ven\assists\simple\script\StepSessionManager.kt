package com.ven.assists.simple.script

import com.google.gson.Gson
import com.ven.assists.simple.App
import com.ven.assists.simple.common.LogWrapper
import com.ven.assists.simple.step.StepTag
import com.ven.assists.stepper.StepManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * 分步会话管理器
 * 管理单步骤的执行和反馈
 */
class StepSessionManager {

    private var currentSession: String? = null
    private var currentExecutor: SingleStepExecutor? = null
    private val mainScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    /**
     * 执行单个步骤
     */
    fun executeStep(jsonMetadata: String) {
        LogWrapper.logAppend("=== 开始执行新步骤 ===")
        LogWrapper.logAppend("接收到步骤元数据")
        
        val stepMetadata = SingleStepParser().parseFromJson(jsonMetadata)

        if (stepMetadata == null) {
            LogWrapper.logAppend("❌ 单步骤元数据解析失败")
            return
        }

        LogWrapper.logAppend("✅ 步骤解析成功: ${stepMetadata.stepName}")
        LogWrapper.logAppend("会话ID: ${stepMetadata.sessionId}")
        LogWrapper.logAppend("步骤ID: ${stepMetadata.stepId}")

        // 停止当前执行的步骤（如果有）
        stopCurrentSession()

        // 创建单步骤执行器
        currentExecutor = SingleStepExecutor(stepMetadata) { feedbackData ->
            // 发送反馈给服务端
            sendFeedbackToServer(feedbackData)
        }

        currentSession = stepMetadata.sessionId

        // 执行单个步骤
        try {
            LogWrapper.logAppend("开始执行步骤...")

            // 直接执行步骤，不通过StepManager的反射机制
            mainScope.launch {
                currentExecutor!!.executeDirectly()
            }
        } catch (e: Exception) {
            LogWrapper.logAppend("❌ 步骤执行异常: ${e.message}")
        }
    }

    /**
     * 发送反馈给服务端
     */
    private fun sendFeedbackToServer(feedbackData: FeedbackData) {
        mainScope.launch {
            try {
                val json = Gson().toJson(feedbackData)
                LogWrapper.logAppend("=== 准备发送反馈数据 ===")
                LogWrapper.logAppend("步骤ID: ${feedbackData.stepId}")
                LogWrapper.logAppend("执行状态: ${feedbackData.executionResult.status}")
                LogWrapper.logAppend("执行消息: ${feedbackData.executionResult.message}")
                LogWrapper.logAppend("UI元素数量: ${feedbackData.currentUI.uiElements.size}")
                LogWrapper.logAppend("日志条数: ${feedbackData.logs.size}")

                // TODO: 这里需要添加实际的网络API调用
                // 目前先模拟发送成功
                LogWrapper.logAppend("📤 反馈数据已准备完成")
                LogWrapper.logAppend("等待服务端发送下一步骤...")
                
                // 可以在这里调用网络API发送反馈
                // val result = App.networkManager.sendStepFeedback(json)
                
            } catch (e: Exception) {
                LogWrapper.logAppend("❌ 发送反馈失败: ${e.message}")
            }
        }
    }

    /**
     * 停止当前会话
     */
    fun stopCurrentSession() {
        if (currentSession != null) {
            LogWrapper.logAppend("停止当前会话: $currentSession")
            StepManager.isStop = true
            currentExecutor = null
            currentSession = null
        }
    }

    /**
     * 获取当前会话ID
     */
    fun getCurrentSessionId(): String? {
        return currentSession
    }

    /**
     * 检查是否有活跃会话
     */
    fun hasActiveSession(): Boolean {
        return currentSession != null && currentExecutor != null
    }
}
