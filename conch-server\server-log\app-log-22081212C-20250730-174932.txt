================================================================================
App日志文件
================================================================================
日志ID: log_1753868972328
设备型号: 22081212C
接收时间: 2025-07-30T17:49:32.328506400
日志长度: 2358 字符
================================================================================

日志内容:
--------------------------------------------------------------------------------
2025-07-30 17:49:15
通知监听中...
2025-07-30 17:49:18
通知监听中...
2025-07-30 17:49:18
通知监听中...
2025-07-30 17:49:21
日志已发送到服务端

2025-07-30 17:49:21
监听到通知：日志发送成功: 日志接收成功2025-07-30 17:49:21
监听到通知：田螺安卓
2025-07-30 17:49:26
发送请求：打开微信
2025-07-30 17:49:26
服务端响应: 脚本数据已发送
2025-07-30 17:49:26
收到服务端脚本数据:
2025-07-30 17:49:26
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}
2025-07-30 17:49:26
=== 开始执行动态脚本 ===
2025-07-30 17:49:26
=== 开始执行新步骤 ===
2025-07-30 17:49:26
接收到步骤元数据
2025-07-30 17:49:26
开始解析单步骤元数据
2025-07-30 17:49:26
解析成功: 启动微信
2025-07-30 17:49:26
✅ 步骤解析成功: 启动微信
2025-07-30 17:49:26
会话ID: session_001
2025-07-30 17:49:26
步骤ID: step_001
2025-07-30 17:49:26
开始执行步骤...
2025-07-30 17:49:26
🚀 开始执行单步骤: 启动微信
2025-07-30 17:49:26
执行动作: LAUNCH_APP
2025-07-30 17:49:26
启动应用: com.tencent.mm
2025-07-30 17:49:26
动作执行成功: 成功启动应用: com.tencent.mm
2025-07-30 17:49:27
检查成功条件，条件数量: 1
2025-07-30 17:49:27
查找文本 '通讯录'，找到 0 个元素
2025-07-30 17:49:27
所有成功条件检查完毕，未满足条件
2025-07-30 17:49:27
⚠️ 成功条件未满足，但单步骤执行完成
2025-07-30 17:49:27
收集反馈数据
2025-07-30 17:49:27
开始收集UI信息
2025-07-30 17:49:27
UI信息收集完成，元素数量: 17
2025-07-30 17:49:27
🛑 单步骤执行完成，停止脚本
2025-07-30 17:49:27
📋 步骤执行结果: tag=-1
2025-07-30 17:49:27
🛑 单步骤执行完成，设置停止标志

2025-07-30 17:49:27
监听到通知：田螺安卓2025-07-30 17:49:27
监听到通知：请求成功: 脚本数据已发送
2025-07-30 17:49:27
=== 准备发送反馈数据 ===
2025-07-30 17:49:27
步骤ID: step_001
2025-07-30 17:49:27
执行状态: SUCCESS
2025-07-30 17:49:27
执行消息: 成功启动应用: com.tencent.mm
2025-07-30 17:49:27
UI元素数量: 17
2025-07-30 17:49:27
日志条数: 20
2025-07-30 17:49:27
📤 反馈数据已准备完成
2025-07-30 17:49:27
等待服务端发送下一步骤...
2025-07-30 17:49:29
📤 步骤执行完成，准备自动发送日志
2025-07-30 17:49:29
🔄 自动触发发送日志功能
--------------------------------------------------------------------------------
日志结束
