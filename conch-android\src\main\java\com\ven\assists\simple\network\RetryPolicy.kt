package com.ven.assists.simple.network

import android.util.Log
import kotlinx.coroutines.delay
import kotlin.math.min
import kotlin.math.pow

/**
 * 重试策略
 */
class RetryPolicy(
    private val maxRetries: Int = 3,
    private val baseDelayMs: Long = 1000L,
    private val maxDelayMs: Long = 30000L,
    private val backoffMultiplier: Double = 2.0,
) {

    companion object {
        private const val TAG = "RetryPolicy"

        /**
         * 默认重试策略
         */
        fun default() = RetryPolicy()

        /**
         * 快速重试策略（用于实时性要求高的操作）
         */
        fun fast() = RetryPolicy(
            maxRetries = 2,
            baseDelayMs = 500L,
            maxDelayMs = 5000L,
            backoffMultiplier = 1.5,
        )

        /**
         * 持久重试策略（用于重要但不紧急的操作）
         */
        fun persistent() = RetryPolicy(
            maxRetries = 5,
            baseDelayMs = 2000L,
            maxDelayMs = 60000L,
            backoffMultiplier = 2.5,
        )
    }

    /**
     * 执行带重试的操作
     */
    suspend fun <T> execute(
        operation: suspend () -> T,
        shouldRetry: (Exception) -> Boolean = { true },
    ): Result<T> {
        var lastException: Exception? = null

        repeat(maxRetries + 1) { attempt ->
            try {
                val result = operation()
                if (attempt > 0) {
                    Log.d(TAG, "操作在第${attempt + 1}次尝试后成功")
                }
                return Result.success(result)
            } catch (e: Exception) {
                lastException = e

                if (attempt < maxRetries && shouldRetry(e)) {
                    val delayMs = calculateDelay(attempt)
                    Log.w(TAG, "操作失败，${delayMs}ms后进行第${attempt + 2}次尝试", e)
                    delay(delayMs)
                } else {
                    Log.e(TAG, "操作在${attempt + 1}次尝试后最终失败", e)
                    return Result.failure(e)
                }
            }
        }

        return Result.failure(lastException ?: Exception("未知错误"))
    }

    /**
     * 计算延迟时间（指数退避算法）
     */
    private fun calculateDelay(attempt: Int): Long {
        val delay = baseDelayMs * backoffMultiplier.pow(attempt).toLong()
        return min(delay, maxDelayMs)
    }

    /**
     * 检查异常是否应该重试
     */
    fun shouldRetryForException(exception: Exception): Boolean {
        return when (exception) {
            is java.net.SocketTimeoutException -> true
            is java.net.ConnectException -> true
            is java.net.UnknownHostException -> true
            is java.io.IOException -> true
            is retrofit2.HttpException -> {
                // HTTP 5xx 错误可以重试，4xx 错误通常不应该重试
                exception.code() >= 500
            }
            else -> false
        }
    }
}

/**
 * 重试策略扩展函数
 */
suspend fun <T> RetryPolicy.retryOnNetworkError(
    operation: suspend () -> T,
): Result<T> {
    return execute(operation) { exception ->
        shouldRetryForException(exception)
    }
}

/**
 * 带重试的网络请求扩展
 */
suspend fun <T> withRetry(
    retryPolicy: RetryPolicy = RetryPolicy.default(),
    operation: suspend () -> T,
): Result<T> {
    return retryPolicy.retryOnNetworkError(operation)
}
