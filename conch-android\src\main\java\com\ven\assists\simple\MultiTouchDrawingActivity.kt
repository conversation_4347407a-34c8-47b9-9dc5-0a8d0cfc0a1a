package com.ven.assists.simple

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.ven.assists.simple.databinding.ActivityMultiTouchDrawingBinding

class MultiTouchDrawingActivity : AppCompatActivity() {
    private val viewBinding: ActivityMultiTouchDrawingBinding by lazy {
        ActivityMultiTouchDrawingBinding.inflate(layoutInflater).apply {
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)
    }
}
