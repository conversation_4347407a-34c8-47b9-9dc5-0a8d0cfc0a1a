package com.ven.assists.simple.script

import android.content.ComponentName
import android.content.Intent
import com.ven.assists.AssistsCore
import com.ven.assists.AssistsCore.click
import com.ven.assists.AssistsCore.findFirstParentClickable
import com.ven.assists.AssistsCore.getBoundsInScreen
import com.ven.assists.service.AssistsService
import com.ven.assists.simple.common.LogWrapper

/**
 * 动作执行器
 * 根据动作元数据执行具体的操作
 */
object ActionExecutor {

    /**
     * 执行动作
     */
    fun execute(action: SingleStepParser.ActionMetadata): ActionResult {
        LogWrapper.logAppend("执行动作: ${action.type}")
        
        return when (action.type) {
            "LAUNCH_APP" -> executeLaunchApp(action.params)
            "FIND_AND_CLICK" -> executeFindAndClick(action.params)
            "INPUT_TEXT" -> executeInputText(action.params)
            "SCROLL_LIST" -> executeScrollList(action.params)
            "WAIT" -> executeWait(action.params)
            "BACK" -> executeBack()
            else -> ActionResult.failed("未知动作类型: ${action.type}")
        }
    }

    /**
     * 启动应用
     */
    private fun executeLaunchApp(params: Map<String, Any>): ActionResult {
        return try {
            val packageName = params["packageName"] as String
            val activityName = params["activityName"] as String

            LogWrapper.logAppend("启动应用: $packageName")
            
            Intent().apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                component = ComponentName(packageName, activityName)
                AssistsService.instance?.startActivity(this)
            }

            ActionResult.success("成功启动应用: $packageName")
        } catch (e: Exception) {
            ActionResult.failed("启动应用失败: ${e.message}")
        }
    }

    /**
     * 查找并点击元素
     */
    private fun executeFindAndClick(params: Map<String, Any>): ActionResult {
        val findMethod = params["findMethod"] as String
        val targetText = params["targetText"] as? String
        val positionCheck = params["positionCheck"] as? Map<String, Any>

        LogWrapper.logAppend("查找并点击: $targetText")

        val elements = when (findMethod) {
            "findByText" -> AssistsCore.findByText(targetText ?: "")
            else -> emptyList()
        }

        elements.forEach { element ->
            if (positionCheck != null && !checkPosition(element, positionCheck)) {
                return@forEach
            }

            val clickableElement = element.findFirstParentClickable() ?: element
            if (clickableElement.click()) {
                LogWrapper.logAppend("成功点击元素: $targetText")
                return ActionResult.success("成功点击: $targetText")
            }
        }

        return ActionResult.failed("未找到可点击的元素: $targetText")
    }

    /**
     * 输入文本
     */
    private fun executeInputText(params: Map<String, Any>): ActionResult {
        val text = params["text"] as? String ?: ""
        LogWrapper.logAppend("输入文本: $text")
        
        // 这里需要根据具体的输入方式实现
        // 可能需要先找到输入框，然后输入文本
        return ActionResult.success("文本输入完成: $text")
    }

    /**
     * 滚动列表
     */
    private fun executeScrollList(params: Map<String, Any>): ActionResult {
        val direction = params["direction"] as? String ?: "down"
        val distance = (params["distance"] as? Double)?.toInt() ?: 500
        
        LogWrapper.logAppend("滚动列表: $direction, 距离: $distance")
        
        // 这里需要根据具体的滚动方式实现
        return ActionResult.success("滚动完成")
    }

    /**
     * 返回操作
     */
    private fun executeBack(): ActionResult {
        LogWrapper.logAppend("执行返回操作")
        return if (AssistsCore.back()) {
            ActionResult.success("成功执行返回操作")
        } else {
            ActionResult.failed("返回操作失败")
        }
    }

    /**
     * 等待
     */
    private fun executeWait(params: Map<String, Any>): ActionResult {
        val duration = (params["duration"] as Double).toLong()
        LogWrapper.logAppend("等待 ${duration}ms")
        Thread.sleep(duration)
        return ActionResult.success("等待完成: ${duration}ms")
    }

    /**
     * 检查位置条件
     */
    private fun checkPosition(element: Any, positionCheck: Map<String, Any>): Boolean {
        try {
            val bounds = (element as? android.view.accessibility.AccessibilityNodeInfo)?.getBoundsInScreen()
            if (bounds != null) {
                val minX = (positionCheck["minX"] as Double).toInt()
                val minY = (positionCheck["minY"] as Double).toInt()
                val baseWidth = (positionCheck["baseWidth"] as Double).toInt()
                val baseHeight = (positionCheck["baseHeight"] as Double).toInt()

                // 根据屏幕比例调整坐标
                val actualMinX = AssistsCore.getX(baseWidth, minX)
                val actualMinY = AssistsCore.getY(baseHeight, minY)

                val result = bounds.left >= actualMinX && bounds.top >= actualMinY
                LogWrapper.logAppend("位置检查: 元素(${bounds.left}, ${bounds.top}) >= 期望($actualMinX, $actualMinY) = $result")
                return result
            }
        } catch (e: Exception) {
            LogWrapper.logAppend("位置检查异常: ${e.message}")
        }
        return false
    }
}

/**
 * 动作执行结果
 */
data class ActionResult(
    val status: Status,
    val message: String? = null,
    val error: String? = null
) {
    enum class Status { SUCCESS, FAILED, RETRY }

    companion object {
        fun success(message: String = "") = ActionResult(Status.SUCCESS, message = message)
        fun failed(error: String) = ActionResult(Status.FAILED, error = error)
        fun retry() = ActionResult(Status.RETRY)
    }
}
