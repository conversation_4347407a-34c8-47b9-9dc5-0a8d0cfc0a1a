package com.ven.assists.simple

import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.ven.assists.simple.network.NetworkManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 网络测试Activity
 * 用于测试与服务端的连接
 */
class NetworkTestActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "NetworkTestActivity"
    }

    private lateinit var networkManager: NetworkManager
    private lateinit var statusText: TextView
    private lateinit var testButton: Button
    private lateinit var sendCommandButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 创建简单的布局
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }

        statusText = TextView(this).apply {
            text = "网络状态: 未知"
            textSize = 16f
            setPadding(0, 20, 0, 20)
        }

        testButton = Button(this).apply {
            text = "测试服务端连接"
            setOnClickListener { testServerConnection() }
        }

        sendCommandButton = Button(this).apply {
            text = "发送测试指令"
            setOnClickListener { sendTestCommand() }
        }

        layout.addView(statusText)
        layout.addView(testButton)
        layout.addView(sendCommandButton)

        setContentView(layout)

        // 初始化网络管理器
        networkManager = NetworkManager.getInstance(this)

        // 检查初始连接状态
        updateConnectionStatus()
    }

    private fun updateConnectionStatus() {
        val isConnected = networkManager.isConnected()
        statusText.text = "网络状态: ${if (isConnected) "已连接" else "未连接"}"
    }

    private fun testServerConnection() {
        statusText.text = "正在测试连接到 192.168.2.2:8080..."

        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始测试服务端连接")
                val result = networkManager.getServerStatus()

                withContext(Dispatchers.Main) {
                    result.onSuccess { status ->
                        val message = "✅ 连接成功!\n" +
                            "服务端状态: ${status.status}\n" +
                            "版本: ${status.server.version}\n" +
                            "名称: ${status.server.name}\n" +
                            "时间: ${status.timestamp}"
                        statusText.text = message
                        Log.d(TAG, "服务端连接成功: $status")
                    }.onFailure { error ->
                        val message = "❌ 连接失败!\n" +
                            "错误: ${error.message}\n" +
                            "类型: ${error.javaClass.simpleName}\n" +
                            "请检查:\n" +
                            "1. 手机和电脑在同一WiFi\n" +
                            "2. 服务端是否运行在192.168.2.2:8080\n" +
                            "3. 防火墙设置"
                        statusText.text = message
                        Log.e(TAG, "服务端连接失败", error)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    val message = "⚠️ 连接异常!\n" +
                        "异常: ${e.message}\n" +
                        "类型: ${e.javaClass.simpleName}"
                    statusText.text = message
                    Log.e(TAG, "连接异常", e)
                }
            }
        }
    }

    private fun sendTestCommand() {
        statusText.text = "正在发送测试指令..."

        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始发送测试指令")
                val result = networkManager.sendCommand(
                    "TEST_COMMAND",
                    """{"message": "这是一个测试指令", "timestamp": ${System.currentTimeMillis()}}""",
                )

                withContext(Dispatchers.Main) {
                    result.onSuccess { response ->
                        val message = "✅ 指令发送成功!\n" +
                            "消息: ${response.message}\n" +
                            "会话ID: ${response.sessionId}\n" +
                            "状态: ${response.state}\n" +
                            "预计时长: ${response.estimatedDuration}ms"
                        statusText.text = message
                        Log.d(TAG, "测试指令发送成功: $response")
                    }.onFailure { error ->
                        val message = "❌ 指令发送失败!\n" +
                            "错误: ${error.message}\n" +
                            "类型: ${error.javaClass.simpleName}"
                        statusText.text = message
                        Log.e(TAG, "测试指令发送失败", error)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    val message = "⚠️ 发送异常!\n" +
                        "异常: ${e.message}\n" +
                        "类型: ${e.javaClass.simpleName}"
                    statusText.text = message
                    Log.e(TAG, "发送异常", e)
                }
            }
        }
    }
}
