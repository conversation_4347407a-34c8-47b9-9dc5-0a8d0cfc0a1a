# 让AI写自动化脚本的前置信息模板

## 1. 功能需求描述

### 1.1 目标应用
- **应用名称**: [如：微信、微博、抖音等]
- **应用版本**: [如：8.0.37，可选]
- **目标平台**: [Android/iOS，通常为Android]

### 1.2 具体功能
- **主要目标**: [要实现什么操作，如：发布朋友圈、自动点赞、收集能量等]
- **操作类型**: [发布内容/浏览内容/数据收集/交互操作等]
- **执行频率**: [单次执行/循环执行/定时执行]

### 1.3 详细操作流程
请按步骤详细描述整个操作过程：
```
步骤1：启动应用
步骤2：导航到目标页面
步骤3：执行具体操作
步骤4：处理结果/返回
...
```

## 2. 应用技术信息

### 2.1 包名和组件信息
```kotlin
// 应用包名
val packageName = "com.example.app"

// 启动Activity（如果知道）
val launchActivity = "com.example.app.MainActivity"

// 完整的ComponentName
component = ComponentName("com.example.app", "com.example.app.MainActivity")
```

### 2.2 关键页面识别
- **主页标识**: [如何判断已进入主页，通常通过特定文本或元素]
- **目标页面标识**: [如何判断已进入目标页面]
- **错误页面处理**: [遇到异常页面如何处理]

## 3. UI界面元素信息

### 3.1 关键按钮和文本
请列出所有需要交互的UI元素：

#### 导航相关
- **底部标签**: [如："微信"、"通讯录"、"发现"、"我"]
- **顶部按钮**: [如："+"、"搜索"、"设置"]
- **返回按钮**: [如："返回"、"取消"、"关闭"]

#### 功能按钮
- **主要操作按钮**: [如："发表"、"发送"、"确定"]
- **次要操作按钮**: [如："从相册选择"、"拍照"、"编辑"]
- **设置选项**: [如："谁可以看"、"仅自己可见"、"公开"]

#### 状态文本
- **成功标识**: [如："发表成功"、"已进入朋友圈"]
- **加载状态**: [如："加载中..."、"稍等片刻..."]
- **错误提示**: [如："网络错误"、"权限不足"]

### 3.2 特殊UI组件
- **列表类型**: [ListView/RecyclerView/其他]
- **输入框类型**: [EditText/其他]
- **特殊控件**: [如：开关、滑块、选择器等]

### 3.3 元素位置信息
如果知道元素的大概位置，请提供：
```kotlin
// 示例：底部导航栏的"发现"按钮
screen.left > AssistsCore.getX(1080, 630) &&
screen.top > AssistsCore.getX(1920, 1850)
```

### 3.4 资源ID（如果知道）
```kotlin
// 示例
"com.tencent.mm:id/bottom_tab"
"android:id/content"
```

## 4. 内容和数据配置

### 4.1 发布内容配置
- **文字内容**: [固定文字/动态生成/用户输入]
- **图片选择**: [第一张/随机选择/指定图片]
- **其他媒体**: [视频/音频/链接等]

### 4.2 动态内容生成
```kotlin
// 示例：时间戳+固定文字
"${TimeUtils.getNowString()}: 这是一条自动发布的内容"
```

### 4.3 配置参数
- **可见性设置**: [公开/好友可见/仅自己可见]
- **标签设置**: [话题标签/位置信息等]
- **其他选项**: [评论权限/转发权限等]

## 5. 异常处理需求

### 5.1 权限处理
- **需要的权限**: [相册权限/相机权限/存储权限等]
- **权限申请流程**: [自动允许/手动处理]
- **权限被拒绝的处理**: [重试/跳过/停止]

### 5.2 网络和加载
- **网络异常**: [重试机制/超时处理]
- **页面加载**: [等待时间/加载完成判断]
- **内容加载**: [图片加载/数据刷新]

### 5.3 界面变化
- **弹窗处理**: [广告弹窗/提示弹窗/更新提示]
- **页面跳转**: [意外跳转/页面刷新]
- **应用崩溃**: [重启应用/错误恢复]

### 5.4 重试机制
- **最大重试次数**: [通常5次]
- **重试间隔**: [延迟时间]
- **失败后的处理**: [返回上一步/完全重启/停止执行]

## 6. 现有框架信息

### 6.1 可用的AssistsCore方法
```kotlin
// UI元素查找
AssistsCore.findByText(text: String)           // 根据文本查找
AssistsCore.findById(id: String)               // 根据ID查找
AssistsCore.findByTags(className: String)      // 根据类名查找
AssistsCore.getAllNodes()                      // 获取所有节点

// 交互操作
node.click()                                   // 点击
node.longClick()                              // 长按
node.paste(text: String)                      // 粘贴文本
node.scrollForward()                          // 向前滚动
node.scrollBackward()                         // 向后滚动

// 手势操作
AssistsCore.gesture(startLocation, endLocation, startTime, duration)  // 手势滑动
AssistsCore.gestureClick(x: Float, y: Float)  // 手势点击

// 系统操作
AssistsCore.back()                            // 返回
AssistsCore.home()                            // 主页
AssistsCore.getPackageName()                  // 获取当前包名

// 位置信息
node.getBoundsInScreen()                      // 获取屏幕位置
node.findFirstParentClickable()               // 查找可点击父元素
AssistsCore.getX(baseWidth, x)                // X坐标转换
AssistsCore.getY(baseHeight, y)               // Y坐标转换

// 调试工具
node.logNode()                                // 打印节点信息
LogWrapper.logAppend(message: String)         // 添加日志
```

### 6.2 步骤框架结构
```kotlin
class YourScript : StepImpl() {
    override fun onImpl(collector: StepCollector) {
        collector.next(StepTag.STEP_1) {
            // 步骤1的逻辑
            return@next Step.get(StepTag.STEP_2)
        }.next(StepTag.STEP_2) {
            // 步骤2的逻辑
            if (条件判断) {
                return@next Step.get(StepTag.STEP_3)
            } else {
                return@next Step.repeat  // 重复当前步骤
            }
        }.next(StepTag.STEP_3) {
            // 最后一步
            return@next Step.none    // 结束执行
        }
    }
}
```

### 6.3 常用模式
```kotlin
// 启动应用模式
Intent().apply {
    addCategory(Intent.CATEGORY_LAUNCHER)
    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    component = ComponentName(packageName, activityName)
    AssistsService.instance?.startActivity(this)
}

// 查找并点击模式
AssistsCore.findByText("按钮文字").forEach {
    val screen = it.getBoundsInScreen()
    if (screen.left > x && screen.top > y) {
        it.findFirstParentClickable()?.click()
        return@next Step.get(下一步)
    }
}

// 错误处理模式
if (AssistsCore.getPackageName() == 目标包名) {
    AssistsCore.back()
    return@next Step.repeat
}
if (it.repeatCount == 5) {
    return@next Step.get(StepTag.STEP_1)  // 重新开始
}
return@next Step.repeat  // 继续重试
```

## 7. 特殊需求

### 7.1 图像识别需求
如果需要使用OpenCV进行图像识别：
- **模板图片**: [需要提供模板图片文件]
- **识别区域**: [屏幕的哪个区域进行识别]
- **匹配阈值**: [识别的准确度要求]

### 7.2 数据存储需求
- **需要保存的数据**: [操作结果/统计信息/配置参数]
- **存储方式**: [文件/数据库/SharedPreferences]
- **数据格式**: [JSON/XML/纯文本]

### 7.3 定时执行需求
- **执行时间**: [固定时间/间隔执行]
- **执行条件**: [特定条件触发]
- **后台运行**: [是否需要后台执行]

## 8. 测试和调试信息

### 8.1 测试设备信息
- **设备型号**: [如：小米11、华为P40等]
- **屏幕分辨率**: [如：1080x1920]
- **Android版本**: [如：Android 11]
- **应用版本**: [测试时使用的应用版本]

### 8.2 已知问题
- **兼容性问题**: [不同设备/版本的差异]
- **稳定性问题**: [容易出错的步骤]
- **性能问题**: [执行速度/资源占用]

### 8.3 调试需求
- **日志级别**: [详细/简单/仅错误]
- **调试信息**: [需要输出哪些调试信息]
- **错误报告**: [如何处理和报告错误]

---

## 填写示例

### 示例：微信朋友圈发布脚本

**功能需求**: 自动发布一条带图片的朋友圈，设置为仅自己可见

**应用信息**:
- 包名: `com.tencent.mm`
- 启动Activity: `com.tencent.mm.ui.LauncherUI`

**关键UI元素**:
- 底部标签: "微信"、"通讯录"、"发现"、"我"
- 朋友圈入口: "朋友圈"
- 发布按钮: "拍照分享"
- 相册选择: "从相册选择"
- 完成按钮: "完成"、"发表"
- 可见性设置: "谁可以看" -> "仅自己可见"

**操作流程**:
1. 启动微信应用
2. 点击"发现"标签
3. 点击"朋友圈"进入朋友圈
4. 点击"拍照分享"按钮
5. 选择"从相册选择"
6. 选择第一张图片并确认
7. 输入文字内容
8. 设置可见性为"仅自己可见"
9. 点击"发表"完成发布

**异常处理**:
- 权限申请: 自动点击"允许"
- 网络加载: 等待加载完成
- 重试机制: 最多重试5次

---

请根据您的具体需求填写以上信息，信息越详细，AI生成的脚本质量越高！