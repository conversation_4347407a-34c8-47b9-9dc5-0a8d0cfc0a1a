package com.ven.assists.simple.network

import android.content.Context
import android.provider.Settings
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络管理器 - 统一管理REST API和WebSocket连接
 */
class NetworkManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "NetworkManager"
        private const val DEFAULT_SERVER_URL = "http://192.168.2.2:8080/"
        private const val DEFAULT_WEBSOCKET_URL = "ws://192.168.2.2:8080/ws"

        @Volatile
        private var INSTANCE: NetworkManager? = null

        fun getInstance(context: Context): NetworkManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private var serverUrl = DEFAULT_SERVER_URL
    private var webSocketUrl = DEFAULT_WEBSOCKET_URL
    private var deviceId: String

    // REST API相关
    private val okHttpClient: OkHttpClient
    private val retrofit: Retrofit
    private val apiService: ApiService

    // WebSocket相关
    private val webSocketClient = ConchWebSocketClient.getInstance()

    // 协程作用域
    private val networkScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    init {
        // 获取设备ID
        deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)

        // 配置OkHttp客户端
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        // 配置Retrofit
        retrofit = Retrofit.Builder()
            .baseUrl(serverUrl)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        apiService = retrofit.create(ApiService::class.java)

        // 初始化WebSocket连接
        initWebSocket()
    }

    /**
     * 设置服务器地址
     */
    fun setServerUrl(url: String, wsUrl: String? = null) {
        serverUrl = url
        webSocketUrl = wsUrl ?: url.replace("http", "ws") + "ws"

        // 重新初始化连接
        webSocketClient.disconnect()
        initWebSocket()
    }

    /**
     * 初始化WebSocket连接
     */
    private fun initWebSocket() {
        webSocketClient.addListener(object : ConchWebSocketClient.WebSocketListener {
            override fun onConnected() {
                Log.d(TAG, "WebSocket连接成功")
                // 发送心跳
                startHeartbeat()
            }

            override fun onDisconnected() {
                Log.d(TAG, "WebSocket连接断开")
                stopHeartbeat()
            }

            override fun onMessageReceived(message: String) {
                Log.d(TAG, "收到服务端消息: $message")
                // 处理服务端消息
                handleServerMessage(message)
            }

            override fun onError(error: Exception) {
                Log.e(TAG, "WebSocket错误", error)
            }
        })

        webSocketClient.connect(webSocketUrl)
    }

    /**
     * 发送指令到服务端
     */
    suspend fun sendCommand(commandType: String, commandData: String): Result<CommandResponseDto> {
        return try {
            val command = CommandRequestDto(
                textCommand = TextCommandDto(
                    text = "$commandType: $commandData",
                    confidence = 1.0,
                    timestamp = System.currentTimeMillis(),
                ),
                deviceInfo = DeviceInfoDto(
                    model = android.os.Build.MODEL,
                    androidVersion = "Android ${android.os.Build.VERSION.RELEASE}",
                    screenResolution = getScreenResolution(),
                    installedApps = emptyList(),
                ),
            )

            // 先通过WebSocket发送
            if (webSocketClient.isConnected()) {
                webSocketClient.sendCommand(command)
            }

            // 然后通过REST API发送（作为备份）
            val response = apiService.sendCommand(command)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("API调用失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "发送指令失败", e)
            Result.failure(e)
        }
    }

    /**
     * 获取服务端状态
     */
    suspend fun getServerStatus(): Result<HealthCheckResponseDto> {
        return try {
            val response = apiService.getServerStatus()
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("获取服务端状态失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取服务端状态失败", e)
            Result.failure(e)
        }
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        return webSocketClient.isConnected()
    }

    /**
     * 发送日志到服务端
     */
    suspend fun uploadLogs(logContent: String): Result<LogUploadResponseDto> {
        return try {
            val logRequest = LogUploadRequestDto(
                deviceInfo = DeviceInfoDto(
                    model = android.os.Build.MODEL,
                    androidVersion = "Android ${android.os.Build.VERSION.RELEASE}",
                    screenResolution = getScreenResolution(),
                    installedApps = emptyList(),
                ),
                logContent = logContent,
                logLevel = "INFO",
                timestamp = System.currentTimeMillis()
            )

            val response = apiService.uploadLogs(logRequest)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("上传日志失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "上传日志失败", e)
            Result.failure(e)
        }
    }

    /**
     * 请求打开微信
     */
    suspend fun requestOpenWechat(): Result<RequestOpenWechatResponseDto> {
        return try {
            val request = RequestOpenWechatDto(
                deviceInfo = DeviceInfoDto(
                    model = android.os.Build.MODEL,
                    androidVersion = "Android ${android.os.Build.VERSION.RELEASE}",
                    screenResolution = getScreenResolution(),
                    installedApps = emptyList(),
                ),
                requestId = "req_${System.currentTimeMillis()}",
                timestamp = System.currentTimeMillis()
            )

            val response = apiService.requestOpenWechat(request)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("请求打开微信失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "请求打开微信失败", e)
            Result.failure(e)
        }
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        stopHeartbeat()
        webSocketClient.disconnect()
        networkScope.cancel()
    }

    // 心跳相关
    private var heartbeatJob: Job? = null

    private fun startHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = networkScope.launch {
            while (isActive) {
                try {
                    val heartbeat = HeartbeatRequest(deviceId, System.currentTimeMillis())
                    apiService.heartbeat(heartbeat)
                    delay(30000) // 30秒心跳间隔
                } catch (e: Exception) {
                    Log.e(TAG, "心跳失败", e)
                    delay(5000) // 失败后5秒重试
                }
            }
        }
    }

    private fun stopHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = null
    }

    private fun generateCommandId(): String {
        return "${deviceId}_${System.currentTimeMillis()}"
    }

    private fun getScreenResolution(): String {
        val displayMetrics = context.resources.displayMetrics
        return "${displayMetrics.widthPixels}x${displayMetrics.heightPixels}"
    }

    private fun handleServerMessage(message: String) {
        // 处理服务端推送的消息
        // 可以根据需要解析JSON并执行相应操作
    }
}
