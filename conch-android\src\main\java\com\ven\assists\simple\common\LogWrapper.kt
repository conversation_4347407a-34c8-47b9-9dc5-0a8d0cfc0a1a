package com.ven.assists.simple.common

import com.blankj.utilcode.util.TimeUtils
import com.ven.assists.utils.CoroutineWrapper
import kotlinx.coroutines.flow.MutableSharedFlow

object LogWrapper {
    var logCache = StringBuilder("")

    val logAppendValue = MutableSharedFlow<Pair<String, String>>()

    fun String.logAppend(): String {
        return logAppend(this)
    }

    fun logAppend(msg: CharSequence): String {
        if (logCache.isNotEmpty()) {
            logCache.append("\n")
        }
        if (logCache.length > 5000) {
            logCache.delete(0, logCache.length - 5000)
        }
        logCache.append(TimeUtils.getNowString())
        logCache.append("\n")
        logCache.append(msg)
        CoroutineWrapper.launch {
            logAppendValue.emit(Pair("\n${TimeUtils.getNowString()}\n$msg", logCache.toString()))
        }
        return msg.toString()
    }

    fun clearLog() {
        logCache = StringBuilder("")
        CoroutineWrapper.launch { logAppendValue.emit(Pair("", "")) }
    }

    /**
     * 获取最近的日志条目
     * @param count 要获取的日志条数，默认10条
     * @return 最近的日志条目列表
     */
    fun getRecentLogs(count: Int = 10): List<String> {
        val logContent = logCache.toString()
        if (logContent.isEmpty()) {
            return emptyList()
        }

        // 按行分割日志内容
        val lines = logContent.split("\n")

        // 过滤出非时间戳行（实际的日志内容）
        val logLines = mutableListOf<String>()
        var i = 0
        while (i < lines.size) {
            val line = lines[i].trim()
            // 跳过时间戳行（格式类似：2025-07-30 16:44:36）
            if (line.matches(Regex("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"))) {
                i++
                // 下一行是实际的日志内容
                if (i < lines.size) {
                    val logContent = lines[i].trim()
                    if (logContent.isNotEmpty()) {
                        logLines.add(logContent)
                    }
                }
            }
            i++
        }

        // 返回最近的count条日志
        return if (logLines.size <= count) {
            logLines
        } else {
            logLines.takeLast(count)
        }
    }
}
