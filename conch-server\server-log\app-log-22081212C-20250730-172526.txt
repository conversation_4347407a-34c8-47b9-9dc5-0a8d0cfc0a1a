================================================================================
App日志文件
================================================================================
日志ID: log_1753867526824
设备型号: 22081212C
接收时间: 2025-07-30T17:25:26.824144100
日志长度: 1388 字符
================================================================================

日志内容:
--------------------------------------------------------------------------------
2025-07-30 17:25:03
通知监听中...
2025-07-30 17:25:06
发送请求：打开微信
2025-07-30 17:25:06
服务端响应: 脚本数据已发送
2025-07-30 17:25:06
收到服务端脚本数据:
2025-07-30 17:25:06
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}
2025-07-30 17:25:06
=== 开始执行动态脚本 ===
2025-07-30 17:25:06
=== 开始执行新步骤 ===
2025-07-30 17:25:06
接收到步骤元数据
2025-07-30 17:25:06
开始解析单步骤元数据
2025-07-30 17:25:06
解析成功: 启动微信
2025-07-30 17:25:06
✅ 步骤解析成功: 启动微信
2025-07-30 17:25:06
会话ID: session_001
2025-07-30 17:25:06
步骤ID: step_001
2025-07-30 17:25:06
开始执行步骤...
2025-07-30 17:25:06
❌ 步骤执行异常: com.ven.assists.simple.script.SingleStepExecutor.<init> []

2025-07-30 17:25:06

监听到通知：请求成功: 脚本数据已发送          
--------------------------------------------------------------------------------
日志结束
