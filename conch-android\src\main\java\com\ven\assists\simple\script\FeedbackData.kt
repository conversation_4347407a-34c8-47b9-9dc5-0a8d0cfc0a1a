package com.ven.assists.simple.script

import com.google.gson.annotations.SerializedName

/**
 * 反馈数据
 * 用于向服务端反馈步骤执行结果和当前UI状态
 */
data class FeedbackData(
    @SerializedName("sessionId")
    val sessionId: String,
    
    @SerializedName("stepId")
    val stepId: String,
    
    @SerializedName("executionResult")
    val executionResult: ExecutionResult,
    
    @SerializedName("currentUI")
    val currentUI: UIInfo,
    
    @SerializedName("logs")
    val logs: List<String>,
    
    @SerializedName("timestamp")
    val timestamp: Long
)

/**
 * 执行结果
 */
data class ExecutionResult(
    @SerializedName("status")
    val status: String,  // SUCCESS, FAILED, RETRY_NEEDED
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("executionTime")
    val executionTime: Long,
    
    @SerializedName("retryCount")
    val retryCount: Int
)
