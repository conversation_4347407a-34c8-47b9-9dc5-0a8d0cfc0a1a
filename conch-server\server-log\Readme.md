# 服务端日志存储目录

当前文件夹用于存储从Android客户端上传的日志文件。

## 功能说明

### 1. 发送当前日志功能

当用户在Android应用中点击"发送当前日志"按钮时：

1. **客户端行为**：
   - 收集当前的日志内容（LogWrapper.logCache）
   - 通过REST API发送到服务端的 `/api/v1/logs/upload` 接口
   - 显示发送状态和结果

2. **服务端行为**：
   - 接收日志数据并在控制台打印："📋 [日志接收] 已收到app发送的日志"
   - 生成唯一的日志ID
   - 将日志保存到此目录下的文件中

### 2. 请求打开微信功能

当用户在Android应用中点击"请求打开微信"按钮时：

1. **客户端行为**：
   - 发送请求到服务端的 `/api/v1/script/request-open-wechat` 接口
   - 接收服务端返回的脚本JSON数据
   - 将脚本数据展示到应用日志中

2. **服务端行为**：
   - 接收请求并在控制台打印："🚀 [脚本请求] 收到打开微信的请求"
   - 返回预定义的微信启动脚本JSON数据
   - 客户端将在日志中显示完整的脚本内容

## 日志文件命名规则

文件名格式：`app-log-{设备型号}-{时间戳}.txt`

例如：`app-log-SM_G973F-20231201-143022.txt`

## 日志文件内容格式

```
================================================================================
App日志文件
================================================================================
日志ID: log_1701423022123
设备型号: SM-G973F
接收时间: 2023-12-01T14:30:22
日志长度: 1234 字符
================================================================================

日志内容:
--------------------------------------------------------------------------------
[实际的日志内容]
--------------------------------------------------------------------------------
日志结束
```

## 测试方法

1. 启动服务端（确保运行在8080端口）
2. 启动Android应用并连接到服务端
3. 在应用中执行一些操作产生日志
4. 点击"发送当前日志"按钮
5. 查看服务端控制台输出和此目录下生成的日志文件