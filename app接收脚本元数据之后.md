# App接收脚本元数据之后的处理方案（分步执行版）

## 📋 数据结构合理性分析

### ✅ 当前服务端JSON格式分析
**服务端当前发送的JSON数据**（来自 `TestController.requestOpenWechat()`）：
```json
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}
```

### 🎯 结论：数据结构完全合理
**服务端当前发送的JSON格式与文档建议的单步骤元数据格式完全一致！**

✅ **优势**：
- 包含完整的单步骤执行信息
- 支持动作参数和超时配置
- 包含成功条件验证机制
- 支持重试配置
- 包含反馈需求标识
- 结构清晰，易于解析和扩展

✅ **无需修改服务端**：当前JSON格式已经完美符合分步执行的设计要求

## 核心思路变更

### 问题分析
如选中的代码所示，AI无法预先知道所有可能的界面状态：
```kotlin
AssistsCore.findByText("朋友圈封面，再点一次可以改封面").forEach {
    LogWrapper.logAppend("已进入朋友圈")
    return@next Step.none
}
AssistsCore.findByText("朋友圈封面，点按两次修改封面").forEach {
    LogWrapper.logAppend("已进入朋友圈，已停止")
    return@next Step.none
}
```

AI第一次执行时无法知道会出现哪种文本，因此**不应该一次性传递完整脚本**。

### 新方案：分步执行 + 实时反馈
- **服务端**：根据目标拆分，一次只传输一个执行步骤
- **客户端**：执行单步骤后，将界面变化和日志反馈给服务端
- **服务端**：根据当前界面信息制定下一步骤
- **循环执行**：直到目标完成

## 1. 单步骤元数据格式

### 1.1 单步骤数据结构
```json
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}
```

### 1.2 第二步示例（基于第一步反馈）
```json
{
  "sessionId": "session_001",
  "stepId": "step_002",
  "stepName": "点击通讯录",
  "stepDescription": "在微信主页点击通讯录标签",
  "action": {
    "type": "FIND_AND_CLICK",
    "params": {
      "findMethod": "findByText",
      "targetText": "通讯录",
      "positionCheck": {
        "minX": 340,
        "minY": 1850,
        "baseWidth": 1080,
        "baseHeight": 1920
      }
    },
    "timeout": 10000
  },
  "successConditions": [
    {
      "type": "FIND_ANY_TEXT",
      "params": {
        "possibleTexts": [
          "新的朋友",
          "群聊",
          "标签",
          "公众号"
        ]
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 5,
    "retryDelay": 2000
  },
  "needsFeedback": true,
  "expectedNextAction": "滚动通讯录列表"
}
```

### 1.3 客户端反馈数据格式
```json
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "executionResult": {
    "status": "SUCCESS",  // SUCCESS, FAILED, RETRY_NEEDED
    "message": "成功启动微信应用",
    "executionTime": 2500,
    "retryCount": 0
  },
  "currentUI": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI",
    "uiElements": [
      {
        "text": "微信",
        "className": "android.widget.TextView",
        "bounds": {"left": 90, "top": 1850, "right": 180, "bottom": 1920},
        "clickable": true
      },
      {
        "text": "通讯录",
        "className": "android.widget.TextView",
        "bounds": {"left": 340, "top": 1850, "right": 430, "bottom": 1920},
        "clickable": true
      },
      {
        "text": "发现",
        "className": "android.widget.TextView",
        "bounds": {"left": 630, "top": 1850, "right": 720, "bottom": 1920},
        "clickable": true
      }
    ],
    "screenSize": {"width": 1080, "height": 1920}
  },
  "logs": [
    "启动微信",
    "已打开微信主页"
  ],
  "timestamp": 1703123456789
}
```

## 2. 分步执行流程

### 2.1 整体交互流程图
```mermaid
sequenceDiagram
    participant S as 服务端AI
    participant C as 客户端App
    participant UI as 目标应用界面

    Note over S,C: 开始执行任务：启动微信并点击通讯录

    S->>C: 发送步骤1：启动微信
    C->>C: 解析并执行启动动作
    C->>UI: 启动微信应用
    UI->>C: 界面变化
    C->>C: 收集UI树和日志
    C->>S: 反馈执行结果+当前界面信息

    S->>S: 分析界面，发现"通讯录"按钮
    S->>C: 发送步骤2：点击通讯录
    C->>C: 解析并执行点击动作
    C->>UI: 点击通讯录按钮
    UI->>C: 界面变化到通讯录页面
    C->>C: 收集新的UI树和日志
    C->>S: 反馈执行结果+通讯录界面信息

    S->>S: 分析界面，发现可滚动列表
    S->>C: 发送步骤3：滚动列表
    C->>C: 解析并执行滚动动作
    C->>UI: 滚动通讯录列表
    UI->>C: 列表滚动
    C->>S: 反馈滚动结果

    Note over S,C: 任务完成
```

### 2.2 客户端处理流程图
```mermaid
graph TD
    A[接收单步骤元数据] --> B[解析步骤数据]
    B --> C[执行单个动作]
    C --> D{执行成功?}
    D -->|否| E[检查重试配置]
    E --> F{可以重试?}
    F -->|是| G[等待延迟后重试]
    G --> C
    F -->|否| H[反馈执行失败]

    D -->|是| I[检查成功条件]
    I --> J{条件满足?}
    J -->|否| K[继续等待或重试]
    K --> I
    J -->|是| L[收集当前UI信息]
    L --> M[收集执行日志]
    M --> N[构建反馈数据]
    N --> O[发送反馈给服务端]
    O --> P[等待下一步骤]
    P --> A

    H --> Q[结束执行]
```

### 2.3 核心处理类

#### SingleStepParser - 单步骤解析器
```kotlin
class SingleStepParser {

    data class SingleStepMetadata(
        val sessionId: String,
        val stepId: String,
        val stepName: String,
        val stepDescription: String,
        val targetApp: AppInfo?,
        val action: ActionMetadata,
        val successConditions: List<ConditionMetadata>,
        val retryConfig: RetryConfig,
        val needsFeedback: Boolean,
        val expectedNextAction: String?
    )

    data class ActionMetadata(
        val type: String,
        val params: Map<String, Any>,
        val timeout: Long
    )

    data class ConditionMetadata(
        val type: String,
        val params: Map<String, Any>
    )

    fun parseFromJson(jsonString: String): SingleStepMetadata? {
        return try {
            Gson().fromJson(jsonString, SingleStepMetadata::class.java)
        } catch (e: Exception) {
            LogWrapper.logAppend("解析单步骤元数据失败: ${e.message}")
            null
        }
    }
}
```

#### SingleStepExecutor - 单步骤执行器
```kotlin
class SingleStepExecutor(
    private val stepMetadata: SingleStepParser.SingleStepMetadata,
    private val feedbackCallback: (FeedbackData) -> Unit
) : StepImpl() {

    override fun onImpl(collector: StepCollector) {
        collector.next(StepTag.STEP_1) { stepContext ->
            LogWrapper.logAppend("执行步骤: ${stepMetadata.stepName}")
            executeStep(stepContext)
        }
    }

    private fun executeStep(context: StepContext): Step {
        // 执行动作
        val actionResult = ActionExecutor.execute(stepMetadata.action)

        when (actionResult.status) {
            ActionResult.SUCCESS -> {
                // 检查成功条件
                if (checkSuccessConditions()) {
                    // 收集反馈数据并发送
                    val feedbackData = collectFeedbackData(actionResult)
                    feedbackCallback(feedbackData)
                    return Step.none // 等待下一步骤
                } else {
                    // 条件未满足，继续等待
                    return Step.repeat
                }
            }
            ActionResult.FAILED -> {
                return handleFailure(context, actionResult.error)
            }
            ActionResult.RETRY -> {
                return Step.repeat
            }
        }
    }

    private fun checkSuccessConditions(): Boolean {
        stepMetadata.successConditions.forEach { condition ->
            when (condition.type) {
                "FIND_TEXT" -> {
                    val targetText = condition.params["targetText"] as String
                    val elements = AssistsCore.findByText(targetText)
                    if (elements.isNotEmpty()) {
                        // 检查位置条件
                        val positionCheck = condition.params["positionCheck"] as? Map<String, Any>
                        if (positionCheck != null) {
                            return elements.any { checkPosition(it, positionCheck) }
                        }
                        return true
                    }
                }
                "FIND_ANY_TEXT" -> {
                    val possibleTexts = condition.params["possibleTexts"] as List<String>
                    return possibleTexts.any { text ->
                        AssistsCore.findByText(text).isNotEmpty()
                    }
                }
            }
        }
        return false
    }

    private fun collectFeedbackData(actionResult: ActionResult): FeedbackData {
        return FeedbackData(
            sessionId = stepMetadata.sessionId,
            stepId = stepMetadata.stepId,
            executionResult = ExecutionResult(
                status = "SUCCESS",
                message = actionResult.message ?: "步骤执行成功",
                executionTime = System.currentTimeMillis(),
                retryCount = 0
            ),
            currentUI = UICollector.collectCurrentUI(),
            logs = LogWrapper.getRecentLogs(),
            timestamp = System.currentTimeMillis()
        )
    }
}
```

#### UICollector - UI信息收集器
```kotlin
object UICollector {

    fun collectCurrentUI(): UIInfo {
        val packageName = AssistsCore.getPackageName()
        val allNodes = AssistsCore.getAllNodes()

        val uiElements = allNodes.mapNotNull { node ->
            val text = node.text?.toString()
            val className = node.className?.toString()
            val bounds = node.getBoundsInScreen()

            if (!text.isNullOrEmpty() || className != null) {
                UIElement(
                    text = text,
                    className = className,
                    bounds = ElementBounds(
                        left = bounds.left,
                        top = bounds.top,
                        right = bounds.right,
                        bottom = bounds.bottom
                    ),
                    clickable = node.isClickable,
                    scrollable = node.isScrollable,
                    viewId = node.viewIdResourceName
                )
            } else null
        }

        return UIInfo(
            packageName = packageName,
            activityName = getCurrentActivityName(),
            uiElements = uiElements,
            screenSize = ScreenSize(
                width = ScreenUtils.getScreenWidth(),
                height = ScreenUtils.getScreenHeight()
            )
        )
    }

    private fun getCurrentActivityName(): String {
        // 通过反射或其他方式获取当前Activity名称
        return "unknown"
    }
}

data class UIInfo(
    val packageName: String,
    val activityName: String,
    val uiElements: List<UIElement>,
    val screenSize: ScreenSize
)

data class UIElement(
    val text: String?,
    val className: String?,
    val bounds: ElementBounds,
    val clickable: Boolean,
    val scrollable: Boolean,
    val viewId: String?
)

data class ElementBounds(
    val left: Int,
    val top: Int,
    val right: Int,
    val bottom: Int
)

data class ScreenSize(
    val width: Int,
    val height: Int
)
```

#### ActionExecutor - 动作执行器（简化版）
```kotlin
object ActionExecutor {

    fun execute(action: SingleStepParser.ActionMetadata): ActionResult {
        return when (action.type) {
            "LAUNCH_APP" -> executeLaunchApp(action.params)
            "FIND_AND_CLICK" -> executeFindAndClick(action.params)
            "INPUT_TEXT" -> executeInputText(action.params)
            "SCROLL_LIST" -> executeScrollList(action.params)
            "WAIT" -> executeWait(action.params)
            "BACK" -> executeBack()
            else -> ActionResult.failed("未知动作类型: ${action.type}")
        }
    }

    // 动作执行方法保持不变，复用现有逻辑
    private fun executeLaunchApp(params: Map<String, Any>): ActionResult {
        return try {
            val packageName = params["packageName"] as String
            val activityName = params["activityName"] as String

            Intent().apply {
                addCategory(Intent.CATEGORY_LAUNCHER)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                component = ComponentName(packageName, activityName)
                AssistsService.instance?.startActivity(this)
            }

            ActionResult.success("成功启动应用: $packageName")
        } catch (e: Exception) {
            ActionResult.failed("启动应用失败: ${e.message}")
        }
    }

    private fun executeFindAndClick(params: Map<String, Any>): ActionResult {
        val findMethod = params["findMethod"] as String
        val targetText = params["targetText"] as? String
        val positionCheck = params["positionCheck"] as? Map<String, Any>

        val elements = when (findMethod) {
            "findByText" -> AssistsCore.findByText(targetText ?: "")
            else -> emptyList()
        }

        elements.forEach { element ->
            if (positionCheck != null && !checkPosition(element, positionCheck)) {
                return@forEach
            }

            val clickableElement = element.findFirstParentClickable() ?: element
            if (clickableElement.click()) {
                LogWrapper.logAppend("成功点击元素: $targetText")
                return ActionResult.success("成功点击: $targetText")
            }
        }

        return ActionResult.failed("未找到可点击的元素: $targetText")
    }

    private fun executeBack(): ActionResult {
        return if (AssistsCore.back()) {
            ActionResult.success("成功执行返回操作")
        } else {
            ActionResult.failed("返回操作失败")
        }
    }

    private fun executeWait(params: Map<String, Any>): ActionResult {
        val duration = (params["duration"] as Double).toLong()
        Thread.sleep(duration)
        return ActionResult.success("等待完成: ${duration}ms")
    }
}

data class ActionResult(
    val status: Status,
    val message: String? = null,
    val error: String? = null
) {
    enum class Status { SUCCESS, FAILED, RETRY }

    companion object {
        fun success(message: String = "") = ActionResult(Status.SUCCESS, message = message)
        fun failed(error: String) = ActionResult(Status.FAILED, error = error)
        fun retry() = ActionResult(Status.RETRY)
    }
}
```

#### StepSessionManager - 分步会话管理器
```kotlin
class StepSessionManager {

    private var currentSession: String? = null
    private var currentExecutor: SingleStepExecutor? = null

    suspend fun executeStep(jsonMetadata: String) {
        val stepMetadata = SingleStepParser().parseFromJson(jsonMetadata)

        if (stepMetadata == null) {
            LogWrapper.logAppend("单步骤元数据解析失败")
            return
        }

        LogWrapper.logAppend("开始执行步骤: ${stepMetadata.stepName}")

        // 创建单步骤执行器
        currentExecutor = SingleStepExecutor(stepMetadata) { feedbackData ->
            // 发送反馈给服务端
            sendFeedbackToServer(feedbackData)
        }

        currentSession = stepMetadata.sessionId

        // 执行单个步骤
        StepManager.execute(currentExecutor!!::class.java, StepTag.STEP_1)
    }

    private suspend fun sendFeedbackToServer(feedbackData: FeedbackData) {
        try {
            val json = Gson().toJson(feedbackData)
            LogWrapper.logAppend("发送反馈数据: ${feedbackData.stepId}")

            // 这里应该调用网络API发送反馈
            // apiService.sendStepFeedback(json)

            // 模拟发送成功
            LogWrapper.logAppend("反馈发送成功，等待下一步骤...")

        } catch (e: Exception) {
            LogWrapper.logAppend("发送反馈失败: ${e.message}")
        }
    }

    fun stopCurrentSession() {
        currentExecutor = null
        currentSession = null
        LogWrapper.logAppend("停止当前会话")
    }
}

data class FeedbackData(
    val sessionId: String,
    val stepId: String,
    val executionResult: ExecutionResult,
    val currentUI: UIInfo,
    val logs: List<String>,
    val timestamp: Long
)

data class ExecutionResult(
    val status: String,
    val message: String,
    val executionTime: Long,
    val retryCount: Int
)
```

## 3. 使用示例

### 3.1 在Activity中使用
```kotlin
class MainActivity : AppCompatActivity() {

    private val sessionManager = StepSessionManager()

    // 接收服务端发送的单步骤指令
    fun onReceiveStepFromServer(jsonMetadata: String) {
        lifecycleScope.launch {
            sessionManager.executeStep(jsonMetadata)
        }
    }

    // 停止当前执行会话
    fun onStopExecution() {
        sessionManager.stopCurrentSession()
    }
}
```

### 3.2 完整的分步执行流程
1. **接收单步骤**: 从服务端接收单个步骤的JSON元数据
2. **解析步骤**: 使用SingleStepParser解析步骤数据
3. **执行动作**: 通过SingleStepExecutor执行单个动作
4. **检查条件**: 验证成功条件是否满足
5. **收集反馈**: 收集当前UI状态和执行日志
6. **发送反馈**: 将反馈数据发送给服务端
7. **等待下一步**: 等待服务端分析后发送下一步骤
8. **循环执行**: 重复上述流程直到任务完成

## 4. 新方案的优势特点

### 4.1 智能适应性
- **实时感知**: AI能够根据当前界面状态制定下一步
- **动态调整**: 无需预知所有可能的界面变化
- **错误恢复**: 基于实际情况进行智能错误处理

### 4.2 技术优势
- **完全复用现有框架**: 基于StepImpl和StepManager
- **轻量级通信**: 每次只传输单个步骤数据
- **实时反馈**: 提供详细的UI状态和执行日志
- **会话管理**: 支持多任务并发和会话隔离

### 4.3 可扩展性
- **动作类型**: 易于添加新的动作类型
- **条件检查**: 支持复杂的成功条件判断
- **反馈机制**: 可扩展反馈数据的内容和格式

## 5. 分步执行架构图

### 5.1 整体交互架构
```mermaid
graph TB
    subgraph "服务端AI"
        A[分析任务目标] --> B[制定第一步骤]
        B --> C[发送单步骤JSON]
        H[接收客户端反馈] --> I[分析当前界面状态]
        I --> J[制定下一步骤]
        J --> C
        I --> K[任务完成判断]
    end

    subgraph "客户端 - conch-android"
        C --> D[StepSessionManager接收]
        D --> E[SingleStepParser解析]
        E --> F[SingleStepExecutor创建]
        F --> G[StepManager执行单步骤]

        subgraph "执行层"
            G --> L[ActionExecutor执行动作]
            L --> M[AssistsCore工具调用]
            M --> N[AccessibilityService操作]
        end

        subgraph "反馈层"
            O[UICollector收集界面]
            P[LogWrapper收集日志]
            Q[构建FeedbackData]
        end

        subgraph "现有框架复用"
            R[StepImpl基类]
            S[StepCollector]
            T[Step状态管理]
        end

        G --> O
        G --> P
        O --> Q
        P --> Q
        Q --> H

        F -.继承.-> R
        G -.使用.-> S
        G -.使用.-> T
    end

    subgraph "目标应用"
        N --> U[微信/其他App界面变化]
        U --> O
    end
```

### 5.2 分步执行时序图
```mermaid
sequenceDiagram
    participant AI as 服务端AI
    participant SSM as StepSessionManager
    participant SSE as SingleStepExecutor
    participant AE as ActionExecutor
    participant UC as UICollector
    participant AC as AssistsCore
    participant App as 目标应用

    Note over AI,App: 任务：启动微信并点击通讯录

    AI->>SSM: 步骤1：启动微信
    SSM->>SSE: 创建执行器
    SSE->>AE: 执行LAUNCH_APP
    AE->>AC: 启动应用
    AC->>App: 启动微信
    App->>AC: 应用启动完成

    SSE->>SSE: 检查成功条件
    SSE->>UC: 收集当前UI信息
    UC->>AC: 获取所有节点
    AC->>UC: 返回UI树
    UC->>SSE: 返回UIInfo

    SSE->>SSM: 构建反馈数据
    SSM->>AI: 发送反馈（包含UI状态）

    AI->>AI: 分析界面，发现"通讯录"按钮
    AI->>SSM: 步骤2：点击通讯录
    SSM->>SSE: 创建新执行器
    SSE->>AE: 执行FIND_AND_CLICK
    AE->>AC: 查找并点击"通讯录"
    AC->>App: 点击通讯录按钮
    App->>AC: 界面跳转到通讯录

    SSE->>UC: 收集新界面信息
    SSE->>SSM: 构建反馈数据
    SSM->>AI: 发送反馈（通讯录界面）

    AI->>AI: 分析界面，发现可滚动列表
    AI->>SSM: 步骤3：滚动列表
    Note over AI,App: 继续后续步骤...
```

### 5.3 关键优势对比

| 特性 | 传统一次性脚本 | 分步执行方案 |
|------|---------------|-------------|
| **适应性** | 需要预知所有界面状态 | 实时感知界面变化 |
| **错误处理** | 预设错误处理逻辑 | 基于实际情况智能处理 |
| **维护成本** | 界面变化需要更新脚本 | AI自动适应界面变化 |
| **执行成功率** | 依赖预设条件准确性 | 基于实时状态判断 |
| **调试难度** | 需要完整重现执行环境 | 每步都有详细反馈 |
| **扩展性** | 需要重新编写脚本 | AI学习新界面模式 |

这种分步执行的方案让AI能够像人类一样，根据当前看到的界面状态来决定下一步操作，大大提高了自动化脚本的智能性和成功率。