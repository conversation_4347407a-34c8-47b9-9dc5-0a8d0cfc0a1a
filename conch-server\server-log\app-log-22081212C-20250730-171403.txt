================================================================================
App日志文件
================================================================================
日志ID: log_1753866843249
设备型号: 22081212C
接收时间: 2025-07-30T17:14:03.249150900
日志长度: 1538 字符
================================================================================

日志内容:
--------------------------------------------------------------------------------
2025-07-30 17:13:06
通知监听中...
2025-07-30 17:13:23
通知监听中...
2025-07-30 17:13:31
日志已发送到服务端

2025-07-30 17:13:31
监听到通知：田螺安卓2025-07-30 17:13:31
监听到通知：日志发送成功: 日志接收成功
2025-07-30 17:13:46
发送请求：打开微信
2025-07-30 17:13:46
服务端响应: 脚本数据已发送
2025-07-30 17:13:46
收到服务端脚本数据:
2025-07-30 17:13:46
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}
2025-07-30 17:13:46
=== 开始执行动态脚本 ===
2025-07-30 17:13:46
=== 开始执行新步骤 ===
2025-07-30 17:13:46
接收到步骤元数据
2025-07-30 17:13:46
开始解析单步骤元数据
2025-07-30 17:13:46
解析成功: 启动微信
2025-07-30 17:13:46
✅ 步骤解析成功: 启动微信
2025-07-30 17:13:46
会话ID: session_001
2025-07-30 17:13:46
步骤ID: step_001
2025-07-30 17:13:46
开始执行步骤...
2025-07-30 17:13:46
❌ 步骤执行异常: com.ven.assists.simple.script.SingleStepExecutor.<init> []

2025-07-30 17:13:46
                   
监听到通知：田螺安卓: 脚本数据已发送          
--------------------------------------------------------------------------------
日志结束
