package com.conch.server.controller

import com.conch.server.config.ConchProperties
import com.conch.server.dto.*
import com.conch.server.util.LogUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = ["*"])
class TestController @Autowired constructor(
    private val conchProperties: ConchProperties
) {

    companion object {
        private val logger = LoggerFactory.getLogger(TestController::class.java)
    }

    @GetMapping("/health")
    fun healthCheck(): HealthCheckResponseDto {
        logger.info("Health Check")
        return HealthCheckResponseDto(
            status = "OK",
            timestamp = LocalDateTime.now(),
            server = ServerInfoDto(
                name = conchProperties.server.name,
                version = conchProperties.server.version,
                description = conchProperties.server.description
            ),
            config = ConfigInfoDto(
                sessionTimeout = conchProperties.session.timeout,
                maxConcurrentSessions = conchProperties.session.maxConcurrent,
                maxScriptActions = conchProperties.script.maxActions,
                defaultActionTimeout = conchProperties.script.defaultTimeout
            )
        )
    }

    @PostMapping("/voice/command")
    fun submitTextCommand(@RequestBody request: CommandRequestDto): CommandResponseDto {
        val sessionId = "session_${System.currentTimeMillis()}"

        // 使用专门的日志工具，确保中文正确显示
        LogUtil.logCommand(
            action = "指令接收",
            deviceModel = request.deviceInfo.model,
            command = request.textCommand.text,
            sessionId = sessionId,
            timestamp = LocalDateTime.now().toString()
        )

        return CommandResponseDto(
            sessionId = sessionId,
            state = "PROCESSING",
            message = "指令已接收，正在处理...",
            estimatedDuration = 10000
        )
    }

    @GetMapping("/script/{sessionId}")
    fun getExecutionScript(@PathVariable sessionId: String): ExecutionScriptDto {
        val scriptId = "script_${System.currentTimeMillis()}"

        // 返回一个简单的测试脚本
        val script = ExecutionScriptDto(
            sessionId = sessionId,
            scriptId = scriptId,
            actions = listOf(
                ActionDto(
                    type = "WAIT",
                    target = ActionTargetDto(
                        type = "COORDINATE",
                        value = "",
                        x = 0,
                        y = 0
                    ),
                    parameters = mapOf("duration" to 2000),
                    timeout = 5000
                ),
                ActionDto(
                    type = "CLICK",
                    target = ActionTargetDto(
                        type = "TEXT",
                        value = "测试按钮",
                        x = 0,
                        y = 0
                    ),
                    parameters = emptyMap(),
                    timeout = 5000
                )
            ),
            metadata = ScriptMetadataDto(
                estimatedDuration = 7000,
                retryCount = 3,
                priority = "NORMAL"
            )
        )

        LogUtil.logScriptGeneration(sessionId, scriptId, script.actions.size)
        return script
    }

    @PostMapping("/feedback")
    fun submitFeedback(@RequestBody feedback: FeedbackRequestDto): ApiResponseDto<String> {
        LogUtil.logFeedback(
            sessionId = feedback.sessionId,
            scriptId = feedback.scriptId,
            status = feedback.executionResult.status,
            executionTime = feedback.executionResult.executionTime,
            completedActions = feedback.executionResult.completedActions,
            totalActions = feedback.executionResult.totalActions
        )

        return ApiResponseDto(
            success = true,
            data = "反馈已接收",
            message = "执行反馈处理成功"
        )
    }

    @PostMapping("/session/complete")
    fun completeSession(@RequestBody completion: Map<String, Any>): Map<String, String> {
        val sessionId = completion["sessionId"] as? String ?: "未知会话"
        val success = completion["success"] as? Boolean ?: false
        val finalMessage = completion["finalMessage"] as? String ?: ""

        println("🎯 [会话完成] 会话执行完成")
        println("🆔 会话ID: $sessionId")
        println("✅ 执行结果: ${if (success) "成功" else "失败"}")
        println("💬 最终消息: $finalMessage")
        println("⏰ 完成时间: ${LocalDateTime.now()}")
        println("🎉 会话已结束")
        println("=".repeat(60))

        return mapOf("status" to "OK")
    }

    @PostMapping("/session/{id}/cancel")
    fun cancelSession(@PathVariable id: String): Map<String, String> {
        println("❌ [会话取消] 用户取消了会话: $id")
        println("⏰ 取消时间: ${LocalDateTime.now()}")
        return mapOf("status" to "OK")
    }

    @PostMapping("/api/v1/logs/upload")
    fun uploadLogs(@RequestBody request: LogUploadRequestDto): LogUploadResponseDto {
        val logId = "log_${System.currentTimeMillis()}"

        // 在控制台打印接收到日志的信息
        println("📋 [日志接收] 已收到app发送的日志")
        println("📱 设备型号: ${request.deviceInfo.model}")
        println("🔢 日志长度: ${request.logContent.length} 字符")
        println("⏰ 接收时间: ${LocalDateTime.now()}")
        println("🆔 日志ID: $logId")

        // 保存日志到文件
        try {
            LogUtil.saveAppLogToFile(
                logId = logId,
                deviceModel = request.deviceInfo.model,
                logContent = request.logContent,
                timestamp = LocalDateTime.now()
            )
            println("💾 日志已保存到文件")
        } catch (e: Exception) {
            logger.error("保存日志文件失败", e)
            println("❌ 保存日志文件失败: ${e.message}")
        }

        println("=".repeat(60))

        return LogUploadResponseDto(
            success = true,
            message = "日志接收成功",
            logId = logId,
            timestamp = System.currentTimeMillis()
        )
    }

    @PostMapping("/api/v1/script/request-open-wechat")
    fun requestOpenWechat(@RequestBody request: RequestOpenWechatDto): RequestOpenWechatResponseDto {
        // 在控制台打印接收到请求的信息
        println("🚀 [脚本请求] 收到打开微信的请求")
        println("📱 设备型号: ${request.deviceInfo.model}")
        println("🆔 请求ID: ${request.requestId}")
        println("⏰ 请求时间: ${LocalDateTime.now()}")

        // 构建打开微信的脚本JSON数据
        val scriptJson = """
        {
          "sessionId": "session_001",
          "stepId": "step_001",
          "stepName": "启动微信",
          "stepDescription": "启动微信应用并验证是否成功打开",
          "targetApp": {
            "packageName": "com.tencent.mm",
            "activityName": "com.tencent.mm.ui.LauncherUI"
          },
          "action": {
            "type": "LAUNCH_APP",
            "params": {
              "packageName": "com.tencent.mm",
              "activityName": "com.tencent.mm.ui.LauncherUI"
            },
            "timeout": 5000
          },
          "successConditions": [
            {
              "type": "FIND_TEXT",
              "params": {
                "targetText": "通讯录",
                "positionCheck": {
                  "minX": 340,
                  "minY": 1850,
                  "baseWidth": 1080,
                  "baseHeight": 1920
                }
              }
            }
          ],
          "retryConfig": {
            "maxRetries": 3,
            "retryDelay": 1000
          },
          "needsFeedback": true,
          "expectedNextAction": "点击通讯录"
        }
        """.trimIndent()

        println("📄 发送脚本数据到客户端")
        println("=".repeat(60))

        return RequestOpenWechatResponseDto(
            success = true,
            message = "脚本数据已发送",
            scriptData = scriptJson,
            timestamp = System.currentTimeMillis()
        )
    }
}
