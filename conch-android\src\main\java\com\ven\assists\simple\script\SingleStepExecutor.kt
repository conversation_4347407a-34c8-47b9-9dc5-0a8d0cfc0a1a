package com.ven.assists.simple.script

import com.ven.assists.AssistsCore
import com.ven.assists.AssistsCore.getBoundsInScreen
import com.ven.assists.simple.common.LogWrapper
import com.ven.assists.simple.step.StepTag
import com.ven.assists.stepper.Step
import com.ven.assists.stepper.StepCollector
import com.ven.assists.stepper.StepImpl

/**
 * 单步骤执行器
 * 基于现有的StepImpl框架，执行单个步骤并提供反馈
 */
class SingleStepExecutor(
    private val stepMetadata: SingleStepParser.SingleStepMetadata,
    private val feedbackCallback: (FeedbackData) -> Unit
) : StepImpl() {

    private var retryCount = 0

    override fun onImpl(collector: StepCollector) {
        collector.next(StepTag.STEP_1) { stepContext ->
            LogWrapper.logAppend("执行步骤: ${stepMetadata.stepName}")
            LogWrapper.logAppend("步骤描述: ${stepMetadata.stepDescription}")
            
            executeStep(stepContext)
        }
    }

    /**
     * 执行单个步骤
     */
    private fun executeStep(context: Any): Step {
        try {
            // 执行动作
            val actionResult = ActionExecutor.execute(stepMetadata.action)

            when (actionResult.status) {
                ActionResult.Status.SUCCESS -> {
                    LogWrapper.logAppend("动作执行成功: ${actionResult.message}")

                    // 等待一段时间让界面稳定
                    Thread.sleep(1000)

                    // 检查成功条件
                    if (checkSuccessConditions()) {
                        LogWrapper.logAppend("成功条件满足，步骤执行完成")
                        
                        // 收集反馈数据并发送
                        if (stepMetadata.needsFeedback) {
                            val feedbackData = collectFeedbackData(actionResult)
                            feedbackCallback(feedbackData)
                        }
                        
                        return Step.none // 等待下一步骤
                    } else {
                        LogWrapper.logAppend("成功条件未满足，继续等待...")
                        Thread.sleep(2000) // 等待2秒后重新检查
                        return Step.repeat
                    }
                }
                ActionResult.Status.FAILED -> {
                    LogWrapper.logAppend("动作执行失败: ${actionResult.error}")
                    return handleFailure(actionResult.error ?: "未知错误")
                }
                ActionResult.Status.RETRY -> {
                    LogWrapper.logAppend("动作需要重试")
                    return handleRetry()
                }
            }
        } catch (e: Exception) {
            LogWrapper.logAppend("步骤执行异常: ${e.message}")
            return handleFailure("步骤执行异常: ${e.message}")
        }
    }

    /**
     * 检查成功条件
     */
    private fun checkSuccessConditions(): Boolean {
        LogWrapper.logAppend("检查成功条件，条件数量: ${stepMetadata.successConditions.size}")
        
        stepMetadata.successConditions.forEach { condition ->
            when (condition.type) {
                "FIND_TEXT" -> {
                    val targetText = condition.params["targetText"] as String
                    val elements = AssistsCore.findByText(targetText)
                    LogWrapper.logAppend("查找文本 '$targetText'，找到 ${elements.size} 个元素")
                    
                    if (elements.isNotEmpty()) {
                        // 检查位置条件
                        val positionCheck = condition.params["positionCheck"] as? Map<String, Any>
                        if (positionCheck != null) {
                            val validElements = elements.filter { checkPosition(it, positionCheck) }
                            LogWrapper.logAppend("位置检查通过的元素数量: ${validElements.size}")
                            return validElements.isNotEmpty()
                        }
                        return true
                    }
                }
                "FIND_ANY_TEXT" -> {
                    val possibleTexts = condition.params["possibleTexts"] as List<String>
                    LogWrapper.logAppend("查找任意文本: $possibleTexts")
                    
                    return possibleTexts.any { text ->
                        val found = AssistsCore.findByText(text).isNotEmpty()
                        if (found) LogWrapper.logAppend("找到文本: $text")
                        found
                    }
                }
            }
        }
        
        LogWrapper.logAppend("所有成功条件检查完毕，未满足条件")
        return false
    }

    /**
     * 检查位置条件
     */
    private fun checkPosition(element: Any, positionCheck: Map<String, Any>): Boolean {
        try {
            val bounds = (element as? android.view.accessibility.AccessibilityNodeInfo)?.getBoundsInScreen()
            if (bounds != null) {
                val minX = (positionCheck["minX"] as Double).toInt()
                val minY = (positionCheck["minY"] as Double).toInt()
                val baseWidth = (positionCheck["baseWidth"] as Double).toInt()
                val baseHeight = (positionCheck["baseHeight"] as Double).toInt()
                
                // 根据屏幕比例调整坐标
                val actualMinX = AssistsCore.getX(baseWidth, minX)
                val actualMinY = AssistsCore.getY(baseHeight, minY)
                
                val result = bounds.left >= actualMinX && bounds.top >= actualMinY
                LogWrapper.logAppend("位置检查: 元素(${bounds.left}, ${bounds.top}) >= 期望($actualMinX, $actualMinY) = $result")
                return result
            }
        } catch (e: Exception) {
            LogWrapper.logAppend("位置检查异常: ${e.message}")
        }
        return false
    }

    /**
     * 处理失败情况
     */
    private fun handleFailure(error: String): Step {
        if (retryCount < stepMetadata.retryConfig.maxRetries) {
            retryCount++
            LogWrapper.logAppend("步骤失败，准备第 $retryCount 次重试")
            return Step.get(StepTag.STEP_1, delay = stepMetadata.retryConfig.retryDelay)
        } else {
            LogWrapper.logAppend("步骤失败，已达到最大重试次数")
            
            // 发送失败反馈
            if (stepMetadata.needsFeedback) {
                val failedResult = ActionResult.failed(error)
                val feedbackData = collectFeedbackData(failedResult)
                feedbackCallback(feedbackData)
            }
            
            return Step.none
        }
    }

    /**
     * 处理重试情况
     */
    private fun handleRetry(): Step {
        if (retryCount < stepMetadata.retryConfig.maxRetries) {
            retryCount++
            LogWrapper.logAppend("动作需要重试，第 $retryCount 次重试")
            return Step.get(StepTag.STEP_1, delay = stepMetadata.retryConfig.retryDelay)
        } else {
            LogWrapper.logAppend("已达到最大重试次数")
            return Step.none
        }
    }

    /**
     * 收集反馈数据
     */
    private fun collectFeedbackData(actionResult: ActionResult): FeedbackData {
        LogWrapper.logAppend("收集反馈数据")
        
        return FeedbackData(
            sessionId = stepMetadata.sessionId,
            stepId = stepMetadata.stepId,
            executionResult = ExecutionResult(
                status = when (actionResult.status) {
                    ActionResult.Status.SUCCESS -> "SUCCESS"
                    ActionResult.Status.FAILED -> "FAILED"
                    ActionResult.Status.RETRY -> "RETRY_NEEDED"
                },
                message = actionResult.message ?: actionResult.error ?: "步骤执行完成",
                executionTime = System.currentTimeMillis(),
                retryCount = retryCount
            ),
            currentUI = UICollector.collectCurrentUI(),
            logs = LogWrapper.getRecentLogs(20), // 获取最近20条日志
            timestamp = System.currentTimeMillis()
        )
    }
}
