================================================================================
App日志文件
================================================================================
日志ID: log_1753865091014
设备型号: 22081212C
接收时间: 2025-07-30T16:44:51.015375400
日志长度: 1256 字符
================================================================================

日志内容:
--------------------------------------------------------------------------------
2025-07-30 16:43:43
监听到通知：当前没有日志内容2025-07-30 16:43:43
监听到通知：田螺安卓
2025-07-30 16:44:02
日志已发送到服务端

2025-07-30 16:44:02
监听到通知：日志发送成功: 日志接收成功2025-07-30 16:44:02
监听到通知：田螺安卓
2025-07-30 16:44:19
日志已发送到服务端

2025-07-30 16:44:19
监听到通知：田螺安卓2025-07-30 16:44:19
监听到通知：日志发送成功: 日志接收成功
2025-07-30 16:44:36
发送请求：打开微信
2025-07-30 16:44:36
服务端响应: 脚本数据已发送
2025-07-30 16:44:36
收到服务端脚本数据:
2025-07-30 16:44:36
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}

2025-07-30 16:44:36
2025-07-30 16:44:36
监听到通知：田螺安卓监听到通知：请求成功: 脚本数据已发送
--------------------------------------------------------------------------------
日志结束
